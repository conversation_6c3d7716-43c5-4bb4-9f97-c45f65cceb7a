<?php

defined('APP_PATH') || define('APP_PATH', realpath('.'));
define('TRANSACTION_TYPE_CREDIT_ID', 1);
define('TRANSACTION_TYPE_DEBIT_ID', 2);
define('TRANSACTION_TYPE_REVERSAL_ID', 3);
define('TRANSACTION_TYPE_VOIDED', 4);
define('TRANSACTION_TYPE_CALLBACKS', 5);
define('TRANSACTION_TYPE_TAXATION', 6);

define('QUERY_TRX_PROCESSING_BATCH_SIZE', 300);

$prefix = "Dash";
$prefix_app = "mossbets_b2b_bo";

#Write

$dbHost = "***********";
$dbUser = "apps_user";
$dbPass = "Tb#<M#BnvBc%ur5q";

#Read
$dbReadHost = "***********";
$dbReadUser = "apps_user";
$dbReadPass = 'Tb#<M#BnvBc%ur5q';

$rabbitHost = "***********"; //"rabbitmq-node-0";
$rabbitUser = "liden";
$rabbitPass = "lID3n";

$redisHost = "ke-pr-redis-ha-1-node-0";
$redisUser = "liden";
$redisAuth = "eGuT7yrbJZ8d";

$secretKey = 'eqaSWPnS5UjmJyhNwJ8oOxCxmc1c5dHI-U85DUhUV6K';
$eSystemKey = 'CC3DF1C95E7A29B2667391C63E9E5';
$authKey='4ia2yg.Gs42I0h7(%!{FjDAt{.R5Z-';

$baseAppUrl = 'https://b2b.mb.mb-gaming.life';

if(ENVIRONMENT=="PROD"){
    $baseAppUrl= getenv('BASE_APP_URL');

    #Write
    $dbHost = getenv('DB_HOST');
    $dbUser = getenv('DB_USER');
    $dbPass = getenv('DB_PASS');

    #Read
    $dbReadHost = getenv('DB_READ_HOST');
    $dbReadUser = getenv('DB_READ_USER');
    $dbReadPass = getenv('DB_READ_PASS');

    $rabbitHost = getenv('RABBIT_HOST');
    $rabbitUser = getenv('RABBIT_USER');
    $rabbitPass = getenv('RABBIT_PASS');

    $redisHost = getenv('REDIS_HOST');
    $redisUser = getenv('REDIS_USER');
    $redisAuth = getenv('REDIS_AUTH');

    $secretKey   = getenv('SECRET_KEY');
    $eSystemKey  = getenv('ESYSTEM_KEY');
    $appKey      = getenv('APP_KEY');
    $eCKey       = getenv('ECKEY');

    $authKey="W&RwQ}#ckljoR;No!yH]aa6Pg{X3W5";
}

$connection1 = [
    'adapter' => 'mysql',
    'host' => $dbHost,
    'username' => $dbUser,
    'password' => $dbPass,
    'dbname' => 'partner_b2b_app',
    'charset' => 'utf8',
    "options" => [\PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true, \PDO::ATTR_PERSISTENT]];

//$connection2 = [
//    'adapter' => 'mysql',
//    'host' => $dbHost,
//    'username' => $dbUser,
//    'password' => $dbPass,
//    'dbname' => 'mossbets_profile',
//    'charset' => 'utf8',
//    "options" => [\PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true, \PDO::ATTR_PERSISTENT]];
//
//$connection3 = [
//    'adapter' => 'mysql',
//    'host' => $dbHost,
//    'username' => $dbUser,
//    'password' => $dbPass,
//    'dbname' => 'mossbets_bonus',
//    'charset' => 'utf8',
//    "options" => [\PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true, \PDO::ATTR_PERSISTENT]];

$lidenWrite = [
    'adapter' => 'mysql',
    'host' => '************',
    'username' => "apps_user",
    'password' => "Tb#<M#BnvBc%ur5q",
    'dbname' => 'liden_app',
    'charset' => 'utf8',
    "options" => [\PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true, \PDO::ATTR_PERSISTENT]
];

return new \Phalcon\Config([
    'dbUser' => $connection1,
//    'dbProfile' => $connection2,
//    'dbProfileRead' => $connection2,
    'dbSms' => $lidenWrite,
    #MailSettings
    'MailSettings' => [
        'Host' => 'smtp.mandrillapp.com',
        'Sender' => '<EMAIL>',
        'Pass' => 'cmygQM4YxFhr7jDsJ_PPEw',
        'PortNumber' => 587,
        'WebMaster' => '<EMAIL>',
    ],
    'application' => [
        'controllersDir' => APP_PATH . '/app/controllers/',
        'cacheDir' => APP_PATH . '/app/utils/',
        'baseUri' => "/$prefix_app/",],
    'logPath' => [
        'location' => "/var/www/logs/mossbets_b2b/",
        "dateFormat" => "Y-m-d H:i:s",
        "output" => "%datetime% - [%level_name%] - %message%\n"],
    #RabbitMQ settings
    'mQueue' => [
        'rabbitServer' => $rabbitHost,
        'rabbitVHost' => "/",
        'rabbitUser' => $rabbitUser,
        'rabbitPass' => $rabbitPass,
        'rabbitPortNo' => "5672",
        'QueueName' => "_QUEUE",
        'QueueExchange' => "_EXCHANGE",
        'QueueRoute' => "_ROUTE",],
    'redis' => [
        'redisServer' => $redisHost,
        'redisUser' => $redisUser,
        'redisAuth' => $redisAuth,
        'redisPortNo' => 6379,],
    'settings' => [
        'appName' => 'MicroApp',
        'systemName' => $prefix,
        'AdminWebURL' => '',
        'Helpline' => '0111135731',
        'WebMaster' => '<EMAIL>',
        'Site' => 'https://b2b.mb.mb-gaming.life/',
        'ServerName' => 'microapp.co',
        'connectTimeout' => 5,
        'timeoutDuration' => 30,
        'SelectRecordLimit' => 15,
        'Authentication' => [
            'NxtPwDate' => 10,
            'MaxAttempts' => 10,
            'MaxResetAttempts' => 10,
            'ResetDelay' => 3, //minutes
            'FailedVerifyDelay' => 15, //minutes
            'NextResetDays' => 30,
            'FailedDelay' => 15, //minutes
            'SuccessTimeout' => 59, //minutes
            'TokenExpiryTime' => 360, //minutes
            'HttpRequestKey' => '~X$Kf+C_og2l!1Z',
            'ApplicationKey' => $authKey,
            'ConsumerKey' => 'h7QIC]MAE<l{E12?q)#f',
            'BCPublicKey' => 'dfHyN*35u26d5@4^',
            'SecretKey' => $secretKey,],
        'Account' => [
            'SuperAdmin' => [
                'emailAddress' => '<EMAIL>',
                'displayName' => 'Josephat Mukuha',
            ],
            'DarajaIPs' => [],
            'SystemKey' => $eSystemKey,
            'Mpesa' => [
                'AllowPayouts' => true,
                'MaxPayments' => 150000,
                'PayinPaybills' => [736736],
                'PayoutPaybills' => [3035907],
                'PayinPaybill' => [
                    '736736' => [
                        'NominatedNumber' => '************',
                        'initiatorName' => 'Brian Mossbets',
                        'SecretKeyApp' => '35hgKCMRVBWplNUu',
                        'ConsumerKeyApp' => 'eEiuLDgnomAsdgqGro0MI6KP3SI6Qa6m'],
                ],
                'PayoutPaybill' => [
                    '3035907' => [
                        'initiatorName' => 'Jose API',
                        'initiatorPwd' => 'Macharia11#',
                        'SecretKeyApp' => 'BQy9t4ncDZF8bBWx',
                        'ConsumerKeyApp' => 'SQmGfu7N4HHAqNBG5e1wdqmbE006ut6P'],
                    '3034303' => [
                        'initiatorName' => 'Brian Tiko B2C',
                        'initiatorPwd' => 'Mossbets11#',
                        'SecretKeyApp' => 'qtQ9NGcHfPG5VyBJ',
                        'ConsumerKeyApp' => '727jZRZLYZAjkjXyTEjulA41cnLCmdy1']
                ],
            ],
        ],

        'Pragmatic' => [
            'secureLogin' => 'mssbrgsltnslmtd_mossbets',
            'ProviderId' => 'PragmaticPlay',
            'SecretKey' => 'DdCf7bEc83894214',
            'API' => 'https://api.prerelease-env.biz/IntegrationService/v3/',
            'Bonus_type'=>15
        ],

        'UserStatus' => [
            'Unverified' => 2,
            'InActive' => 0,
            'Active' => 1,
            'Dormant' => 4,
            'Suspended' => 3,
        ],
        'uploads' => [
            'Sms' => [
                'processing' => '/var/www/utilities/sms/processing/',
                'Headers' => 'mobile,full_names,custom1,custom2,custom3',
            ],
            'Deposit' => [
                'processing' => '/var/www/utilities/deposit/processing/',
                'Headers' => '',
            ],
            'Withdraw' => [
                'processing' => '/var/www/utilities/withdraw/processing/',
                'Headers' => '',
            ],
            'UploadsDir' => '/var/www/utilities/uploads/',
        ],
        'mnoApps' => [
            'DefaultDialCode' => '254',
            'DefaultSenderId' => 'LIDEN_OTP',
            'DefaultSenders' => ['MOSSBETS', 'LIDENCO', 'LIDEN_OTP', '23311'],
            'Urls' => [
                'AuthUrl' => 'https://api.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials',
                'PayoutUrl' => 'https://api.safaricom.co.ke/mpesa/b2c/v1/paymentrequest',
                'PullRegisterUrl' => 'https://api.safaricom.co.ke/pulltransactions/v1/register',
                'BulkBroadcastUrl' => 'https://app.apiproxy.co/sms/v1/bulk_broadcast',
                'BulkAuthUrl' => 'https://app.apiproxy.co/account/v1/grant_access',
            ],
            'CallBacks' => [
                'MpesaCBURL' => "$baseAppUrl/wh/v1/checkout/{transactionId}",
                'PayoutCBURL' => "$baseAppUrl/wh/v1/b2c/{transactionId}",
                'ResultCBURL' => "$baseAppUrl/wh/v1/b2c/{transactionId}",
                'SmsCBURL' => "$baseAppUrl/wh/v1/sms/{transactionId}",],
        ],
        'UssdApp' => [#ussd settings
            'ErrorMessage' => 'Sorry, service is experiencing delays. Please try again after sometime.',
            'ExitMenu' => 'Thank you for interacting with the service.',
            'ExpiryPeriod' => [//In Minutes
                'SMS' => 60,
                'USSD' => 5,
                'WHATSAPP' => 30,
            ],
        ],
        'Queues' => [
            'Sms' => [
                'Queue' => 'MOSSBETS_OUTBOX',
                'Route' => 'MOSSBETS_OUTBOX',
                'Exchange' => 'MOSSBETS_OUTBOX'
            ],
            'Checkout' => [
                'Queue' => 'MOSSBETS_CHECKOUT',
                'Route' => 'MOSSBETS_CHECKOUT',
                'Exchange' => 'MOSSBETS_CHECKOUT'
            ],
        ],
        'uploadDir' => [
            'Sms' => '/var/www/utilities/sms/processing/',
            'Deposit' => '/var/www/utilities/deposit/processing/',
            'Withdraw' => '/var/www/utilities/withdraw/processing/',
            'UploadsDir' => '/var/www/utilities/uploads/',
            'SmsHeaders' => 'mobile,full_names,custom1,custom2,custom3',
        ],
        'Message' => [
            'purchase' => "Dear {Fname},{line}you have purchased"
            . "{line}{currency}.{amount}"
            . "{line}Product:{Pname}"
            . "{line}Investment Group:{Gname}{line}-{line}"
            . "Total Assets:{total_assets}."
            . "{line}TrxnId:{trxnId}{line}CS helpline. {helpline}"
        ],
    ],]);
